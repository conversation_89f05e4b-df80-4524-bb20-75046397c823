#!/usr/bin/env python3
"""
Interactive Visualizer Module
Provides interactive chart creation functionality for options analysis
"""

import os
from datetime import datetime

try:
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    from plotly.offline import plot
    PLOTLY_AVAILABLE = True
except ImportError:
    PLOTLY_AVAILABLE = False


class InteractiveVisualizer:
    """
    Creates interactive charts for options analysis using Plotly
    """
    
    def __init__(self):
        if not PLOTLY_AVAILABLE:
            raise ImportError("Plotly is required for InteractiveVisualizer. Install with: pip install plotly")
    
    def create_gex_vex_dashboard(self, data, spot_price, analysis_results, ticker):
        """
        Create a comprehensive GEX/VEX dashboard
        
        Args:
            data: DataFrame with options data including strikes, GEX, VEX
            spot_price: Current underlying price
            analysis_results: Dict with total_gex, total_vex, gex_plus
            ticker: Stock ticker symbol
            
        Returns:
            str: Path to the created HTML file
        """
        # Create subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('GEX by Strike', 'VEX by Strike', 'Combined GEX/VEX', 'Key Metrics'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": True}, {"type": "table"}]]
        )
        
        # GEX Chart
        fig.add_trace(
            go.Bar(x=data['strike'], y=data['GEX'], name='GEX', marker_color='green'),
            row=1, col=1
        )
        
        # VEX Chart  
        fig.add_trace(
            go.Bar(x=data['strike'], y=data['VEX'], name='VEX', marker_color='red'),
            row=1, col=2
        )
        
        # Combined Chart
        fig.add_trace(
            go.Bar(x=data['strike'], y=data['GEX'], name='GEX Combined', marker_color='green'),
            row=2, col=1
        )
        fig.add_trace(
            go.Bar(x=data['strike'], y=data['VEX'], name='VEX Combined', marker_color='red'),
            row=2, col=1
        )
        
        # Add spot price line
        fig.add_vline(x=spot_price, line_dash="dash", line_color="yellow", 
                     annotation_text=f"Spot: ${spot_price:.2f}")
        
        # Metrics Table
        metrics_data = [
            ['Total GEX', f"${analysis_results['total_gex']:,.0f}"],
            ['Total VEX', f"${analysis_results['total_vex']:,.0f}"],
            ['GEX+', f"${analysis_results['gex_plus']:,.0f}"],
            ['Spot Price', f"${spot_price:.2f}"]
        ]
        
        fig.add_trace(
            go.Table(
                header=dict(values=['Metric', 'Value']),
                cells=dict(values=list(zip(*metrics_data)))
            ),
            row=2, col=2
        )
        
        # Update layout
        fig.update_layout(
            title=f"{ticker} Options GEX/VEX Dashboard - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            height=800,
            showlegend=True
        )
        
        # Save to file
        filename = f"{ticker}_gex_vex_dashboard_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        filepath = os.path.join(os.getcwd(), filename)
        plot(fig, filename=filepath, auto_open=True)
        
        return filepath
    
    def create_single_chart(self, data, spot_price, chart_type, ticker):
        """
        Create a single chart for GEX, VEX, or GEX+
        
        Args:
            data: DataFrame with options data
            spot_price: Current underlying price  
            chart_type: 'gex', 'vex', or 'gex_plus'
            ticker: Stock ticker symbol
            
        Returns:
            str: Path to the created HTML file
        """
        fig = go.Figure()
        
        if chart_type.lower() == 'gex':
            fig.add_trace(go.Bar(x=data['strike'], y=data['GEX'], name='GEX', marker_color='green'))
            title = f"{ticker} Gamma Exposure (GEX)"
            y_title = "GEX ($)"
        elif chart_type.lower() == 'vex':
            fig.add_trace(go.Bar(x=data['strike'], y=data['VEX'], name='VEX', marker_color='red'))
            title = f"{ticker} Vega Exposure (VEX)"
            y_title = "VEX ($)"
        elif chart_type.lower() == 'gex_plus':
            # Filter for positive GEX values
            gex_plus_data = data[data['GEX'] > 0]
            fig.add_trace(go.Bar(x=gex_plus_data['strike'], y=gex_plus_data['GEX'], 
                               name='GEX+', marker_color='lightgreen'))
            title = f"{ticker} Positive Gamma Exposure (GEX+)"
            y_title = "GEX+ ($)"
        else:
            raise ValueError(f"Unknown chart_type: {chart_type}")
        
        # Add spot price line
        fig.add_vline(x=spot_price, line_dash="dash", line_color="yellow",
                     annotation_text=f"Spot: ${spot_price:.2f}")
        
        # Update layout
        fig.update_layout(
            title=f"{title} - {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            xaxis_title="Strike Price",
            yaxis_title=y_title,
            height=600
        )
        
        # Save to file
        filename = f"{ticker}_{chart_type.lower()}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        filepath = os.path.join(os.getcwd(), filename)
        plot(fig, filename=filepath, auto_open=True)
        
        return filepath
